import { NextRequest, NextResponse } from 'next/server';

// Dynamic import to ensure <PERSON> is only loaded on server-side
async function loadNewman() {
  const { NewmanWrapper } = await import('@/lib/newman-wrapper');
  return NewmanWrapper;
}

export async function POST(request: NextRequest) {
  try {
    // Ensure this only runs on server-side
    if (typeof window !== 'undefined') {
      return NextResponse.json(
        { error: 'This endpoint can only be called from server-side' },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { collection, environment, ...otherOptions } = body;

    // Validate required fields
    if (!collection) {
      return NextResponse.json(
        { error: 'Collection URL or object is required' },
        { status: 400 }
      );
    }

    // Dynamically load Newman wrapper (server-side only)
    const NewmanWrapper = await loadNewman();

    // Create Newman options
    const options = {
      collection,
      environment,
      reporters: ['cli'], // Default to CLI reporter for simplicity
      color: 'off', // Disable colors for API response
      timeout: 120000, // 2 minutes timeout
      timeoutRequest: 30000, // 30 seconds per request
      ...otherOptions
    };

    // Validate options
    const validation = NewmanWrapper.validateOptions(options);
    if (!validation.valid) {
      return NextResponse.json(
        {
          error: 'Invalid Newman options',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Run the collection
    const result = await NewmanWrapper.runCollection(options);

    // Extract metrics if successful
    let metrics = null;
    if (result.success && result.summary) {
      metrics = NewmanWrapper.extractMetrics(result.summary);
    }

    // Return the result
    return NextResponse.json({
      success: result.success,
      summary: result.summary,
      metrics,
      error: result.error ? {
        name: result.error.name,
        message: result.error.message
      } : null
    });

  } catch (error) {
    console.error('Newman API Error:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS if needed
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
