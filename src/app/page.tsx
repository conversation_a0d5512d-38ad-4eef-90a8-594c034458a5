'use client';

import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';

interface NewmanResult {
  success: boolean;
  summary?: any;
  error?: Error;
  metrics?: {
    totalRequests: number;
    failedRequests: number;
    successfulRequests: number;
    requestSuccessRate: string;
    totalTests: number;
    failedTests: number;
    testSuccessRate: string;
    totalAssertions: number;
    failedAssertions: number;
    assertionSuccessRate: string;
    averageResponseTime: number;
    failures: Array<{
      message: string;
      test?: string;
      source?: string;
    }>;
  };
}

export default function Home() {
  const [collectionUrl, setCollectionUrl] = useState('');
  const [isRunning, setIsRunning] = useState(false);
  const [result, setResult] = useState<NewmanResult | null>(null);
  const [logs, setLogs] = useState<string[]>([]);

  const runNewmanCollection = async () => {
    if (!collectionUrl.trim()) {
      alert('Please enter a collection URL');
      return;
    }

    setIsRunning(true);
    setResult(null);
    setLogs(['Starting Newman collection run...']);

    try {
      const response = await fetch('/api/newman/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          collection: collectionUrl.trim(),
          reporters: ['cli'],
          color: 'off'
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
        setLogs(prev => [...prev, 'Collection run completed successfully!']);
      } else {
        setResult({
          success: false,
          error: new Error(data.error || 'Unknown error occurred')
        });
        setLogs(prev => [...prev, `Error: ${data.error || 'Unknown error occurred'}`]);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Network error occurred';
      setResult({
        success: false,
        error: new Error(errorMessage)
      });
      setLogs(prev => [...prev, `Error: ${errorMessage}`]);
    } finally {
      setIsRunning(false);
    }
  };

  const formatJson = (obj: any) => {
    return JSON.stringify(obj, null, 2);
  };

  return (
    <div className="min-h-screen p-8 max-w-4xl mx-auto">
      <div className="space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold tracking-tight">
            Newman Collection Runner
          </h1>
          <p className="text-lg text-muted-foreground">
            Run Postman collections using Newman with a simple interface
          </p>
        </div>

        {/* Input Section */}
        <div className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="collection-url" className="text-sm font-medium">
              Collection URL
            </label>
            <Input
              id="collection-url"
              type="url"
              placeholder="https://www.getpostman.com/collections/your-collection-id"
              value={collectionUrl}
              onChange={(e) => setCollectionUrl(e.target.value)}
              disabled={isRunning}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              Enter a Postman collection URL or API endpoint that returns a collection JSON
            </p>
          </div>

          <Button
            onClick={runNewmanCollection}
            disabled={isRunning || !collectionUrl.trim()}
            className="w-full sm:w-auto"
          >
            {isRunning ? 'Running Collection...' : 'Run Collection'}
          </Button>
        </div>

        {/* Results Section */}
        {(result || logs.length > 0) && (
          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="results">
              <AccordionTrigger className="text-left">
                <div className="flex items-center gap-2">
                  <span>Collection Run Results</span>
                  {result && (
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      result.success
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {result.success ? 'Success' : 'Failed'}
                    </span>
                  )}
                </div>
              </AccordionTrigger>
              <AccordionContent>
                <div className="space-y-6">
                  {/* Logs */}
                  {logs.length > 0 && (
                    <div className="space-y-2">
                      <h3 className="font-semibold">Execution Log</h3>
                      <div className="bg-muted p-4 rounded-lg font-mono text-sm max-h-40 overflow-y-auto">
                        {logs.map((log, index) => (
                          <div key={index} className="mb-1">
                            {log}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Results Summary */}
                  {result && result.metrics && (
                    <div className="space-y-4">
                      <h3 className="font-semibold">Summary</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-muted p-4 rounded-lg">
                          <h4 className="font-medium text-sm text-muted-foreground">Requests</h4>
                          <p className="text-2xl font-bold">{result.metrics.totalRequests}</p>
                          <p className="text-sm">
                            {result.metrics.successfulRequests} passed, {result.metrics.failedRequests} failed
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Success Rate: {result.metrics.requestSuccessRate}%
                          </p>
                        </div>

                        <div className="bg-muted p-4 rounded-lg">
                          <h4 className="font-medium text-sm text-muted-foreground">Tests</h4>
                          <p className="text-2xl font-bold">{result.metrics.totalTests}</p>
                          <p className="text-sm">
                            {result.metrics.totalTests - result.metrics.failedTests} passed, {result.metrics.failedTests} failed
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Success Rate: {result.metrics.testSuccessRate}%
                          </p>
                        </div>

                        <div className="bg-muted p-4 rounded-lg">
                          <h4 className="font-medium text-sm text-muted-foreground">Assertions</h4>
                          <p className="text-2xl font-bold">{result.metrics.totalAssertions}</p>
                          <p className="text-sm">
                            {result.metrics.totalAssertions - result.metrics.failedAssertions} passed, {result.metrics.failedAssertions} failed
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Success Rate: {result.metrics.assertionSuccessRate}%
                          </p>
                        </div>
                      </div>

                      <div className="bg-muted p-4 rounded-lg">
                        <h4 className="font-medium text-sm text-muted-foreground">Performance</h4>
                        <p className="text-lg font-semibold">
                          Average Response Time: {result.metrics.averageResponseTime}ms
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Failures */}
                  {result && result.metrics && result.metrics.failures.length > 0 && (
                    <div className="space-y-2">
                      <h3 className="font-semibold text-red-600 dark:text-red-400">
                        Failures ({result.metrics.failures.length})
                      </h3>
                      <div className="space-y-2">
                        {result.metrics.failures.map((failure, index) => (
                          <div key={index} className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800">
                            <p className="font-medium text-red-800 dark:text-red-200">
                              {failure.message}
                            </p>
                            {failure.test && (
                              <p className="text-sm text-red-600 dark:text-red-300">
                                Test: {failure.test}
                              </p>
                            )}
                            {failure.source && (
                              <p className="text-xs text-red-500 dark:text-red-400">
                                Source: {failure.source}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Raw Results */}
                  {result && result.summary && (
                    <div className="space-y-2">
                      <h3 className="font-semibold">Raw Results</h3>
                      <details className="bg-muted p-4 rounded-lg">
                        <summary className="cursor-pointer font-medium">
                          View Full Newman Summary
                        </summary>
                        <pre className="mt-4 text-xs overflow-x-auto whitespace-pre-wrap">
                          {formatJson(result.summary)}
                        </pre>
                      </details>
                    </div>
                  )}

                  {/* Error Details */}
                  {result && !result.success && result.error && (
                    <div className="space-y-2">
                      <h3 className="font-semibold text-red-600 dark:text-red-400">Error Details</h3>
                      <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800">
                        <p className="font-mono text-sm text-red-800 dark:text-red-200">
                          {result.error.message}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        )}

        {/* Example URLs */}
        <div className="space-y-2">
          <h3 className="font-semibold text-sm">Example Collection URLs:</h3>
          <div className="space-y-1 text-xs text-muted-foreground">
            <button
              onClick={() => setCollectionUrl('https://www.getpostman.com/collections/631643-f695cab7-6878-eb55-7943-ad88e1ccfd65-JsLv')}
              className="block hover:text-foreground transition-colors cursor-pointer text-left"
              disabled={isRunning}
            >
              • Sample Postman Echo Collection
            </button>
            <p>• Your own collection URL from Postman Cloud API</p>
            <p>• Any URL that returns a valid Postman collection JSON</p>
          </div>
        </div>
      </div>
    </div>
  );
}
