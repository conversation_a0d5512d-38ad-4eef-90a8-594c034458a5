import fs from 'fs';
import path from 'path';
import { <PERSON><PERSON>unOption<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from './newman-handler';

/**
 * Utility functions for Newman operations
 */
export class NewmanUtils {
  
  /**
   * Create a basic Newman configuration
   */
  static createBasicConfig(
    collectionPath: string,
    environmentPath?: string,
    options: Partial<NewmanRunOptions> = {}
  ): NewmanRunOptions {
    return {
      collection: collectionPath,
      environment: environmentPath,
      reporters: ['cli'],
      color: 'auto',
      ...options
    };
  }

  /**
   * Create a configuration for CI/CD environments
   */
  static createCIConfig(
    collectionPath: string,
    environmentPath?: string,
    reportDir: string = './reports'
  ): NewmanRunOptions {
    // Ensure report directory exists
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    return {
      collection: collectionPath,
      environment: environmentPath,
      reporters: ['cli', 'json', 'junit'],
      reporter: {
        cli: {
          silent: false,
          noSummary: false,
          noFailures: false
        },
        json: {
          export: path.join(reportDir, 'newman-report.json')
        },
        junit: {
          export: path.join(reportDir, 'newman-junit.xml')
        }
      },
      color: 'off', // Disable colors for CI logs
      bail: ['failure'], // Stop on first failure in CI
      suppressExitCode: false // Allow proper exit codes for CI
    };
  }

  /**
   * Create configuration for load testing
   */
  static createLoadTestConfig(
    collectionPath: string,
    iterations: number = 10,
    delayMs: number = 100
  ): NewmanRunOptions {
    return {
      collection: collectionPath,
      iterationCount: iterations,
      delayRequest: delayMs,
      reporters: ['cli', 'json'],
      reporter: {
        json: {
          export: './reports/load-test-report.json'
        }
      },
      timeout: 600000, // 10 minutes total timeout
      timeoutRequest: 30000, // 30 seconds per request
      bail: false // Don't stop on failures during load testing
    };
  }

  /**
   * Parse Newman summary and extract key metrics
   */
  static extractMetrics(summary: NewmanRunSummary) {
    const { stats, failures, executions } = summary.run;
    
    return {
      // Request metrics
      totalRequests: stats.requests.total,
      failedRequests: stats.requests.failed,
      successfulRequests: stats.requests.total - stats.requests.failed,
      requestSuccessRate: ((stats.requests.total - stats.requests.failed) / stats.requests.total * 100).toFixed(2),
      
      // Test metrics
      totalTests: stats.tests.total,
      failedTests: stats.tests.failed,
      successfulTests: stats.tests.total - stats.tests.failed,
      testSuccessRate: stats.tests.total > 0 ? ((stats.tests.total - stats.tests.failed) / stats.tests.total * 100).toFixed(2) : '0',
      
      // Assertion metrics
      totalAssertions: stats.assertions.total,
      failedAssertions: stats.assertions.failed,
      successfulAssertions: stats.assertions.total - stats.assertions.failed,
      assertionSuccessRate: stats.assertions.total > 0 ? ((stats.assertions.total - stats.assertions.failed) / stats.assertions.total * 100).toFixed(2) : '0',
      
      // Iteration metrics
      totalIterations: stats.iterations.total,
      failedIterations: stats.iterations.failed,
      
      // Performance metrics
      averageResponseTime: this.calculateAverageResponseTime(executions),
      totalResponseTime: this.calculateTotalResponseTime(executions),
      
      // Failure details
      failureCount: failures.length,
      failures: failures.map(f => ({
        message: f.error.message,
        test: f.error.test,
        source: f.source.name
      }))
    };
  }

  /**
   * Calculate average response time from executions
   */
  private static calculateAverageResponseTime(executions: any[]): number {
    if (!executions || executions.length === 0) return 0;
    
    const totalTime = executions.reduce((sum, exec) => {
      return sum + (exec.response?.responseTime || 0);
    }, 0);
    
    return Math.round(totalTime / executions.length);
  }

  /**
   * Calculate total response time from executions
   */
  private static calculateTotalResponseTime(executions: any[]): number {
    if (!executions || executions.length === 0) return 0;
    
    return executions.reduce((sum, exec) => {
      return sum + (exec.response?.responseTime || 0);
    }, 0);
  }

  /**
   * Generate a summary report from Newman results
   */
  static generateSummaryReport(summary: NewmanRunSummary): string {
    const metrics = this.extractMetrics(summary);
    
    let report = `
=== NEWMAN TEST SUMMARY ===
Collection: ${summary.collection.name}
Environment: ${summary.environment?.name || 'None'}

REQUEST SUMMARY:
- Total Requests: ${metrics.totalRequests}
- Successful: ${metrics.successfulRequests}
- Failed: ${metrics.failedRequests}
- Success Rate: ${metrics.requestSuccessRate}%

TEST SUMMARY:
- Total Tests: ${metrics.totalTests}
- Successful: ${metrics.successfulTests}
- Failed: ${metrics.failedTests}
- Success Rate: ${metrics.testSuccessRate}%

ASSERTION SUMMARY:
- Total Assertions: ${metrics.totalAssertions}
- Successful: ${metrics.successfulAssertions}
- Failed: ${metrics.failedAssertions}
- Success Rate: ${metrics.assertionSuccessRate}%

PERFORMANCE:
- Average Response Time: ${metrics.averageResponseTime}ms
- Total Response Time: ${metrics.totalResponseTime}ms
- Total Iterations: ${metrics.totalIterations}
`;

    if (metrics.failures.length > 0) {
      report += `\nFAILURES (${metrics.failures.length}):\n`;
      metrics.failures.forEach((failure, index) => {
        report += `${index + 1}. ${failure.message}\n`;
        if (failure.test) report += `   Test: ${failure.test}\n`;
        if (failure.source) report += `   Source: ${failure.source}\n`;
      });
    }

    return report;
  }

  /**
   * Save summary report to file
   */
  static async saveSummaryReport(
    summary: NewmanRunSummary, 
    filePath: string = './reports/summary.txt'
  ): Promise<void> {
    const report = this.generateSummaryReport(summary);
    const dir = path.dirname(filePath);
    
    // Ensure directory exists
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(filePath, report, 'utf8');
  }

  /**
   * Check if a collection file exists and is valid JSON
   */
  static validateCollectionFile(filePath: string): { valid: boolean; error?: string } {
    try {
      if (!fs.existsSync(filePath)) {
        return { valid: false, error: 'Collection file does not exist' };
      }

      const content = fs.readFileSync(filePath, 'utf8');
      const collection = JSON.parse(content);

      if (!collection.info || !collection.info.name) {
        return { valid: false, error: 'Invalid collection format - missing info.name' };
      }

      if (!collection.item || !Array.isArray(collection.item)) {
        return { valid: false, error: 'Invalid collection format - missing or invalid items' };
      }

      return { valid: true };
    } catch (error) {
      return { 
        valid: false, 
        error: `Error validating collection: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }

  /**
   * Check if an environment file exists and is valid JSON
   */
  static validateEnvironmentFile(filePath: string): { valid: boolean; error?: string } {
    try {
      if (!fs.existsSync(filePath)) {
        return { valid: false, error: 'Environment file does not exist' };
      }

      const content = fs.readFileSync(filePath, 'utf8');
      const environment = JSON.parse(content);

      if (!environment.name) {
        return { valid: false, error: 'Invalid environment format - missing name' };
      }

      if (!environment.values || !Array.isArray(environment.values)) {
        return { valid: false, error: 'Invalid environment format - missing or invalid values' };
      }

      return { valid: true };
    } catch (error) {
      return { 
        valid: false, 
        error: `Error validating environment: ${error instanceof Error ? error.message : 'Unknown error'}` 
      };
    }
  }

  /**
   * Create environment variables from key-value pairs
   */
  static createEnvironmentVariables(variables: Record<string, string>): Array<{ key: string; value: string }> {
    return Object.entries(variables).map(([key, value]) => ({ key, value }));
  }

  /**
   * Merge multiple environment variable arrays
   */
  static mergeEnvironmentVariables(
    ...variableArrays: Array<Array<{ key: string; value: string }>>
  ): Array<{ key: string; value: string }> {
    const merged = new Map<string, string>();
    
    variableArrays.forEach(variables => {
      variables.forEach(({ key, value }) => {
        merged.set(key, value);
      });
    });
    
    return Array.from(merged.entries()).map(([key, value]) => ({ key, value }));
  }

  /**
   * Run collection with retry logic
   */
  static async runWithRetry(
    options: NewmanRunOptions,
    maxRetries: number = 3,
    retryDelay: number = 1000
  ) {
    let lastError: Error | undefined;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Attempt ${attempt}/${maxRetries}`);
        const result = await NewmanHandler.runCollection(options);
        
        if (result.success) {
          return result;
        }
        
        lastError = result.error;
        
        if (attempt < maxRetries) {
          console.log(`Attempt ${attempt} failed, retrying in ${retryDelay}ms...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < maxRetries) {
          console.log(`Attempt ${attempt} failed with error, retrying in ${retryDelay}ms...`);
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }
    
    throw lastError || new Error('All retry attempts failed');
  }
}

export default NewmanUtils;
