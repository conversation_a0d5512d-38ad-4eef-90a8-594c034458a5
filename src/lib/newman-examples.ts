import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, NewmanEventHandlers } from './newman-handler';

/**
 * Example usage of <PERSON>
 */
export class NewmanExamples {
  
  /**
   * Basic collection run example
   */
  static async basicRun() {
    try {
      const result = await NewmanHandler.runSimple(
        './collections/my-collection.json',
        './environments/dev-environment.json'
      );

      if (result.success) {
        console.log('Collection run completed successfully!');
        console.log(`Total requests: ${result.summary?.run.stats.requests.total}`);
        console.log(`Failed requests: ${result.summary?.run.stats.requests.failed}`);
      } else {
        console.error('Collection run failed:', result.error?.message);
      }

      return result;
    } catch (error) {
      console.error('Error running collection:', error);
      throw error;
    }
  }

  /**
   * Advanced collection run with custom options
   */
  static async advancedRun() {
    const options: NewmanRunOptions = {
      collection: 'https://api.getpostman.com/collections/your-collection-id',
      environment: './environments/production.json',
      iterationCount: 3,
      delayRequest: 1000, // 1 second delay between requests
      timeout: 300000, // 5 minutes total timeout
      timeoutRequest: 30000, // 30 seconds per request
      reporters: ['cli', 'json', 'junit'],
      reporter: {
        json: {
          export: './reports/newman-report.json'
        },
        junit: {
          export: './reports/newman-report.xml'
        }
      },
      globalVar: [
        { key: 'baseUrl', value: 'https://api.example.com' },
        { key: 'apiKey', value: process.env.API_KEY || '' }
      ],
      envVar: [
        { key: 'environment', value: 'production' }
      ],
      bail: ['failure'], // Stop on first failure
      insecure: false, // Verify SSL certificates
      color: 'auto'
    };

    // Event handlers for monitoring the run
    const eventHandlers: NewmanEventHandlers = {
      start: (err, args) => {
        console.log('Collection run started');
      },
      beforeRequest: (err, args) => {
        console.log(`Making request to: ${args.request.url}`);
      },
      request: (err, args) => {
        if (err) {
          console.error(`Request failed: ${err.message}`);
        } else {
          console.log(`Request completed: ${args.response.code} ${args.response.status}`);
        }
      },
      assertion: (err, args) => {
        if (err) {
          console.error(`Assertion failed: ${args.assertion}`);
        } else {
          console.log(`Assertion passed: ${args.assertion}`);
        }
      },
      done: (err, summary) => {
        if (err) {
          console.error('Collection run completed with errors');
        } else {
          console.log('Collection run completed successfully');
          console.log(`Total assertions: ${summary.run.stats.assertions.total}`);
          console.log(`Failed assertions: ${summary.run.stats.assertions.failed}`);
        }
      }
    };

    try {
      const result = await NewmanHandler.runCollection(options, eventHandlers);
      return result;
    } catch (error) {
      console.error('Error in advanced run:', error);
      throw error;
    }
  }

  /**
   * Run collection with data file for multiple iterations
   */
  static async dataFileRun() {
    const options: NewmanRunOptions = {
      collection: './collections/api-tests.json',
      environment: './environments/test.json',
      iterationData: './data/test-data.csv', // CSV or JSON data file
      iterationCount: 5,
      reporters: ['cli', 'json'],
      reporter: {
        json: {
          export: './reports/data-driven-report.json'
        }
      }
    };

    const eventHandlers: NewmanEventHandlers = {
      beforeIteration: (err, args) => {
        console.log(`Starting iteration ${args.cursor.iteration + 1}`);
      },
      iteration: (err, args) => {
        console.log(`Completed iteration ${args.cursor.iteration + 1}`);
      }
    };

    return await NewmanHandler.runCollection(options, eventHandlers);
  }

  /**
   * Run specific folder from collection
   */
  static async folderRun() {
    const options: NewmanRunOptions = {
      collection: './collections/comprehensive-tests.json',
      folder: ['Authentication', 'User Management'], // Run only these folders
      environment: './environments/staging.json',
      reporters: ['cli']
    };

    return await NewmanHandler.runCollection(options);
  }

  /**
   * Run with SSL client certificates
   */
  static async sslClientCertRun() {
    const options: NewmanRunOptions = {
      collection: './collections/secure-api-tests.json',
      environment: './environments/secure.json',
      sslClientCert: './certs/client.crt',
      sslClientKey: './certs/client.key',
      sslClientPassphrase: process.env.SSL_PASSPHRASE,
      sslExtraCaCerts: './certs/ca-bundle.pem',
      reporters: ['cli', 'json'],
      reporter: {
        json: {
          export: './reports/ssl-test-report.json'
        }
      }
    };

    return await NewmanHandler.runCollection(options);
  }

  /**
   * Run with custom request agents (e.g., proxy)
   */
  static async proxyRun() {
    // Note: You would need to install and import appropriate proxy agents
    // Example: npm install socks-proxy-agent
    // const SocksProxyAgent = require('socks-proxy-agent');
    // const requestAgent = new SocksProxyAgent({ host: 'localhost', port: '1080' });

    const options: NewmanRunOptions = {
      collection: './collections/proxy-tests.json',
      environment: './environments/proxy.json',
      // requestAgents: {
      //   http: requestAgent,
      //   https: requestAgent
      // },
      reporters: ['cli']
    };

    return await NewmanHandler.runCollection(options);
  }

  /**
   * Validate options before running
   */
  static async validateAndRun(options: NewmanRunOptions) {
    const validation = NewmanHandler.validateOptions(options);
    
    if (!validation.valid) {
      console.error('Invalid options:', validation.errors);
      throw new Error(`Invalid Newman options: ${validation.errors.join(', ')}`);
    }

    console.log('Options validated successfully');
    return await NewmanHandler.runCollection(options);
  }

  /**
   * Run collection and generate comprehensive report
   */
  static async runWithComprehensiveReporting() {
    const options: NewmanRunOptions = {
      collection: './collections/api-suite.json',
      environment: './environments/test.json',
      iterationCount: 1,
      reporters: ['cli', 'json', 'junit'],
      reporter: {
        cli: {
          silent: false,
          noAssertions: false,
          noSummary: false,
          noFailures: false,
          noConsole: false
        },
        json: {
          export: './reports/detailed-report.json'
        },
        junit: {
          export: './reports/junit-report.xml'
        }
      },
      color: 'on'
    };

    const eventHandlers: NewmanEventHandlers = {
      console: (err, args) => {
        console.log(`[CONSOLE] ${args.messages.join(' ')}`);
      },
      exception: (err, args) => {
        console.error(`[EXCEPTION] ${args.error.message}`);
      }
    };

    try {
      const result = await NewmanHandler.runCollection(options, eventHandlers);
      
      // Process results
      if (result.summary) {
        const { stats, failures } = result.summary.run;
        
        console.log('\n=== RUN SUMMARY ===');
        console.log(`Total Requests: ${stats.requests.total}`);
        console.log(`Failed Requests: ${stats.requests.failed}`);
        console.log(`Total Assertions: ${stats.assertions.total}`);
        console.log(`Failed Assertions: ${stats.assertions.failed}`);
        
        if (failures.length > 0) {
          console.log('\n=== FAILURES ===');
          failures.forEach((failure, index) => {
            console.log(`${index + 1}. ${failure.error.message}`);
            console.log(`   At: ${failure.at}`);
            console.log(`   Source: ${failure.source.name}`);
          });
        }
      }

      return result;
    } catch (error) {
      console.error('Comprehensive run failed:', error);
      throw error;
    }
  }
}

// Export utility functions
export const runNewmanCollection = NewmanHandler.runCollection;
export const runNewmanSimple = NewmanHandler.runSimple;
export const validateNewmanOptions = NewmanHandler.validateOptions;
