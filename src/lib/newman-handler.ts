import * as newman from 'newman';
import { EventEmitter } from 'events';

// Newman Run Options Interface
export interface NewmanRunOptions {
  // Required
  collection: string | object;
  
  // Optional Environment & Variables
  environment?: string | object;
  envVar?: Array<{ key: string; value: string }> | { key: string; value: string };
  globals?: string | object;
  globalVar?: Array<{ key: string; value: string }> | { key: string; value: string };
  
  // Iteration Options
  iterationCount?: number;
  iterationData?: string;
  
  // Execution Options
  folder?: string | string[];
  workingDir?: string;
  insecureFileRead?: boolean;
  
  // Timeout Options (in milliseconds)
  timeout?: number;
  timeoutRequest?: number;
  timeoutScript?: number;
  delayRequest?: number;
  
  // Request Options
  ignoreRedirects?: boolean;
  insecure?: boolean;
  
  // Control Flow Options
  bail?: boolean | ['folder'] | ['failure'];
  suppressExitCode?: boolean;
  
  // Reporting Options
  reporters?: string | string[];
  reporter?: {
    [reporterName: string]: {
      export?: string;
      template?: string;
      [key: string]: any;
    };
  };
  color?: 'on' | 'off' | 'auto';
  
  // SSL Options
  sslClientCert?: string;
  sslClientKey?: string;
  sslClientPassphrase?: string;
  sslClientCertList?: string | string[];
  sslExtraCaCerts?: string;
  
  // Advanced Options
  requestAgents?: {
    http?: any;
    https?: any;
  };
  cookieJar?: string | object;
  newmanVersion?: string;
}

// Newman Run Statistics Interface
export interface NewmanRunStats {
  iterations: {
    total: number;
    pending: number;
    failed: number;
  };
  items: {
    total: number;
    pending: number;
    failed: number;
  };
  scripts: {
    total: number;
    pending: number;
    failed: number;
  };
  prerequests: {
    total: number;
    pending: number;
    failed: number;
  };
  requests: {
    total: number;
    pending: number;
    failed: number;
  };
  tests: {
    total: number;
    pending: number;
    failed: number;
  };
  assertions: {
    total: number;
    pending: number;
    failed: number;
  };
  testScripts: {
    total: number;
    pending: number;
    failed: number;
  };
  prerequestScripts: {
    total: number;
    pending: number;
    failed: number;
  };
}

// Newman Failure Interface
export interface NewmanFailure {
  error: {
    name: string;
    message: string;
    test?: string;
    checksum?: string;
    index?: number;
    type?: string;
  };
  at: string;
  source: {
    name?: string;
    request?: {
      url?: string;
      method?: string;
    };
  };
}

// Newman Execution Interface
export interface NewmanExecution {
  item: {
    name: string;
    id: string;
  };
  request: {
    url: string;
    method: string;
    header: Array<{ key: string; value: string }>;
    body?: any;
  };
  response: {
    code: number;
    status: string;
    header: Array<{ key: string; value: string }>;
    body: string;
    responseTime: number;
    responseSize: number;
  };
  assertions?: Array<{
    assertion: string;
    error?: {
      name: string;
      message: string;
    };
  }>;
}

// Newman Run Summary Interface
export interface NewmanRunSummary {
  error?: {
    name: string;
    message: string;
  };
  collection: {
    name: string;
    id: string;
    description?: string;
    variables?: Array<{ key: string; value: string }>;
  };
  environment?: {
    name: string;
    id: string;
    values: Array<{ key: string; value: string; enabled: boolean }>;
  };
  globals?: {
    name: string;
    id: string;
    values: Array<{ key: string; value: string; enabled: boolean }>;
  };
  run: {
    stats: NewmanRunStats;
    failures: NewmanFailure[];
    executions: NewmanExecution[];
  };
}

// Newman Event Types
export type NewmanEventType = 
  | 'start'
  | 'beforeIteration'
  | 'beforeItem'
  | 'beforePrerequest'
  | 'prerequest'
  | 'beforeRequest'
  | 'request'
  | 'beforeTest'
  | 'test'
  | 'beforeScript'
  | 'script'
  | 'item'
  | 'iteration'
  | 'assertion'
  | 'console'
  | 'exception'
  | 'beforeDone'
  | 'done';

// Newman Event Handler Interface
export interface NewmanEventHandlers {
  start?: (err: Error | null, args: any) => void;
  beforeIteration?: (err: Error | null, args: any) => void;
  beforeItem?: (err: Error | null, args: any) => void;
  beforePrerequest?: (err: Error | null, args: any) => void;
  prerequest?: (err: Error | null, args: any) => void;
  beforeRequest?: (err: Error | null, args: any) => void;
  request?: (err: Error | null, args: any) => void;
  beforeTest?: (err: Error | null, args: any) => void;
  test?: (err: Error | null, args: any) => void;
  beforeScript?: (err: Error | null, args: any) => void;
  script?: (err: Error | null, args: any) => void;
  item?: (err: Error | null, args: any) => void;
  iteration?: (err: Error | null, args: any) => void;
  assertion?: (err: Error | null, args: any) => void;
  console?: (err: Error | null, args: any) => void;
  exception?: (err: Error | null, args: any) => void;
  beforeDone?: (err: Error | null, args: any) => void;
  done?: (err: Error | null, summary: NewmanRunSummary) => void;
}

// Newman Handler Result Interface
export interface NewmanHandlerResult {
  success: boolean;
  summary?: NewmanRunSummary;
  error?: Error;
  emitter: EventEmitter;
}

/**
 * Newman Handler Class
 * Provides a TypeScript wrapper around Newman with proper type safety
 */
export class NewmanHandler {
  /**
   * Run a Postman collection using Newman
   * @param options Newman run options
   * @param eventHandlers Optional event handlers
   * @returns Promise with run result
   */
  static async runCollection(
    options: NewmanRunOptions,
    eventHandlers?: NewmanEventHandlers
  ): Promise<NewmanHandlerResult> {
    return new Promise((resolve) => {
      const emitter = newman.run(options, (err: Error | null, summary: NewmanRunSummary) => {
        resolve({
          success: !err && !summary.error,
          summary,
          error: err || summary.error || undefined,
          emitter
        });
      });

      // Attach event handlers if provided
      if (eventHandlers) {
        Object.entries(eventHandlers).forEach(([event, handler]) => {
          if (handler) {
            emitter.on(event, handler);
          }
        });
      }
    });
  }

  /**
   * Run a collection with simplified options
   * @param collectionPath Path to collection file or URL
   * @param environmentPath Optional path to environment file
   * @param options Additional options
   * @returns Promise with run result
   */
  static async runSimple(
    collectionPath: string,
    environmentPath?: string,
    options: Partial<NewmanRunOptions> = {}
  ): Promise<NewmanHandlerResult> {
    const runOptions: NewmanRunOptions = {
      collection: collectionPath,
      environment: environmentPath,
      reporters: ['cli'],
      ...options
    };

    return this.runCollection(runOptions);
  }

  /**
   * Validate Newman options
   * @param options Options to validate
   * @returns Validation result
   */
  static validateOptions(options: NewmanRunOptions): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!options.collection) {
      errors.push('Collection is required');
    }

    if (options.iterationCount && options.iterationCount < 1) {
      errors.push('Iteration count must be greater than 0');
    }

    if (options.timeout && options.timeout < 0) {
      errors.push('Timeout must be non-negative');
    }

    if (options.timeoutRequest && options.timeoutRequest < 0) {
      errors.push('Request timeout must be non-negative');
    }

    if (options.timeoutScript && options.timeoutScript < 0) {
      errors.push('Script timeout must be non-negative');
    }

    if (options.delayRequest && options.delayRequest < 0) {
      errors.push('Delay request must be non-negative');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}

// Export default instance
export default NewmanHandler;
