import * as newman from 'newman';
import { EventEmitter } from 'events';

// Re-export Newman types for convenience
export type NewmanRunOptions = newman.NewmanRunOptions;
export type NewmanRunSummary = newman.NewmanRunSummary;

// Custom interfaces for our wrapper
export interface NewmanHandlerResult {
  success: boolean;
  summary?: NewmanRunSummary;
  error?: Error;
  emitter: EventEmitter;
}

// Simplified metrics interface
export interface NewmanMetrics {
  totalRequests: number;
  failedRequests: number;
  successfulRequests: number;
  requestSuccessRate: string;
  totalTests: number;
  failedTests: number;
  testSuccessRate: string;
  totalAssertions: number;
  failedAssertions: number;
  assertionSuccessRate: string;
  averageResponseTime: number;
  totalResponseTime: number;
  failureCount: number;
  failures: Array<{
    message: string;
    test?: string;
    source?: string;
  }>;
}

/**
 * Newman Wrapper Class
 * Provides a simplified TypeScript wrapper around Newman
 */
export class NewmanWrapper {
  /**
   * Run a Postman collection using Newman
   * @param options Newman run options
   * @returns Promise with run result
   */
  static async runCollection(options: NewmanRunOptions): Promise<NewmanHandlerResult> {
    return new Promise((resolve) => {
      const emitter = newman.run(options, (err: Error | null, summary: NewmanRunSummary) => {
        resolve({
          success: !err && !summary.error,
          summary,
          error: err || summary.error || undefined,
          emitter
        });
      });
    });
  }

  /**
   * Run a collection with simplified options
   * @param collectionPath Path to collection file or URL
   * @param environmentPath Optional path to environment file
   * @param additionalOptions Additional options
   * @returns Promise with run result
   */
  static async runSimple(
    collectionPath: string,
    environmentPath?: string,
    additionalOptions: Partial<NewmanRunOptions> = {}
  ): Promise<NewmanHandlerResult> {
    const options: NewmanRunOptions = {
      collection: collectionPath,
      environment: environmentPath,
      reporters: ['cli'],
      ...additionalOptions
    };

    return this.runCollection(options);
  }

  /**
   * Extract metrics from Newman summary
   */
  static extractMetrics(summary: NewmanRunSummary): NewmanMetrics {
    const { stats, failures, executions } = summary.run;
    
    return {
      // Request metrics
      totalRequests: stats.requests.total,
      failedRequests: stats.requests.failed,
      successfulRequests: stats.requests.total - stats.requests.failed,
      requestSuccessRate: ((stats.requests.total - stats.requests.failed) / stats.requests.total * 100).toFixed(2),
      
      // Test metrics
      totalTests: stats.tests.total,
      failedTests: stats.tests.failed,
      testSuccessRate: stats.tests.total > 0 ? ((stats.tests.total - stats.tests.failed) / stats.tests.total * 100).toFixed(2) : '0',
      
      // Assertion metrics
      totalAssertions: stats.assertions.total,
      failedAssertions: stats.assertions.failed,
      assertionSuccessRate: stats.assertions.total > 0 ? ((stats.assertions.total - stats.assertions.failed) / stats.assertions.total * 100).toFixed(2) : '0',
      
      // Performance metrics
      averageResponseTime: this.calculateAverageResponseTime(executions),
      totalResponseTime: this.calculateTotalResponseTime(executions),
      
      // Failure details
      failureCount: failures.length,
      failures: failures.map(f => ({
        message: f.error.message,
        test: f.error.test,
        source: f.source?.name
      }))
    };
  }

  /**
   * Calculate average response time from executions
   */
  private static calculateAverageResponseTime(executions: any[]): number {
    if (!executions || executions.length === 0) return 0;
    
    const totalTime = executions.reduce((sum, exec) => {
      return sum + (exec.response?.responseTime || 0);
    }, 0);
    
    return Math.round(totalTime / executions.length);
  }

  /**
   * Calculate total response time from executions
   */
  private static calculateTotalResponseTime(executions: any[]): number {
    if (!executions || executions.length === 0) return 0;
    
    return executions.reduce((sum, exec) => {
      return sum + (exec.response?.responseTime || 0);
    }, 0);
  }

  /**
   * Validate Newman options
   * @param options Options to validate
   * @returns Validation result
   */
  static validateOptions(options: NewmanRunOptions): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!options.collection) {
      errors.push('Collection is required');
    }

    if (options.iterationCount && options.iterationCount < 1) {
      errors.push('Iteration count must be greater than 0');
    }

    if (options.timeout && options.timeout < 0) {
      errors.push('Timeout must be non-negative');
    }

    if (options.timeoutRequest && options.timeoutRequest < 0) {
      errors.push('Request timeout must be non-negative');
    }

    if (options.timeoutScript && options.timeoutScript < 0) {
      errors.push('Script timeout must be non-negative');
    }

    if (options.delayRequest && options.delayRequest < 0) {
      errors.push('Delay request must be non-negative');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Create a basic Newman configuration
   */
  static createBasicConfig(
    collectionPath: string,
    environmentPath?: string,
    options: Partial<NewmanRunOptions> = {}
  ): NewmanRunOptions {
    return {
      collection: collectionPath,
      environment: environmentPath,
      reporters: ['cli'],
      color: 'auto',
      ...options
    };
  }

  /**
   * Create a configuration for CI/CD environments
   */
  static createCIConfig(
    collectionPath: string,
    environmentPath?: string,
    reportDir: string = './reports'
  ): NewmanRunOptions {
    return {
      collection: collectionPath,
      environment: environmentPath,
      reporters: ['cli', 'json', 'junit'],
      reporter: {
        json: {
          export: `${reportDir}/newman-report.json`
        },
        junit: {
          export: `${reportDir}/newman-junit.xml`
        }
      },
      color: 'off', // Disable colors for CI logs
      bail: ['failure'], // Stop on first failure in CI
      suppressExitCode: false // Allow proper exit codes for CI
    };
  }

  /**
   * Generate a summary report from Newman results
   */
  static generateSummaryReport(summary: NewmanRunSummary): string {
    const metrics = this.extractMetrics(summary);
    
    let report = `
=== NEWMAN TEST SUMMARY ===
Collection: ${summary.collection.name}
Environment: ${summary.environment?.name || 'None'}

REQUEST SUMMARY:
- Total Requests: ${metrics.totalRequests}
- Successful: ${metrics.successfulRequests}
- Failed: ${metrics.failedRequests}
- Success Rate: ${metrics.requestSuccessRate}%

TEST SUMMARY:
- Total Tests: ${metrics.totalTests}
- Successful: ${metrics.totalTests - metrics.failedTests}
- Failed: ${metrics.failedTests}
- Success Rate: ${metrics.testSuccessRate}%

ASSERTION SUMMARY:
- Total Assertions: ${metrics.totalAssertions}
- Successful: ${metrics.totalAssertions - metrics.failedAssertions}
- Failed: ${metrics.failedAssertions}
- Success Rate: ${metrics.assertionSuccessRate}%

PERFORMANCE:
- Average Response Time: ${metrics.averageResponseTime}ms
- Total Response Time: ${metrics.totalResponseTime}ms
`;

    if (metrics.failures.length > 0) {
      report += `\nFAILURES (${metrics.failures.length}):\n`;
      metrics.failures.forEach((failure, index) => {
        report += `${index + 1}. ${failure.message}\n`;
        if (failure.test) report += `   Test: ${failure.test}\n`;
        if (failure.source) report += `   Source: ${failure.source}\n`;
      });
    }

    return report;
  }
}

export default NewmanWrapper;
