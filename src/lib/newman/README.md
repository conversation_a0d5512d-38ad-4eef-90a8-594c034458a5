# Newman Handler for TypeScript

A comprehensive TypeScript wrapper for the <PERSON> library that provides type-safe interfaces for running Postman collections programmatically.

## Features

- **Type Safety**: Full TypeScript support with comprehensive interfaces
- **Event Handling**: Support for all Newman events with proper typing
- **Validation**: Built-in validation for Newman options
- **Utilities**: Helper functions for common operations
- **Examples**: Ready-to-use examples for various scenarios
- **Reporting**: Enhanced reporting capabilities
- **Error Handling**: Robust error handling and retry mechanisms

## Installation

Newman is already included as a dependency in this project. The handler files are located in `src/lib/newman/`.

## Quick Start

### Basic Usage

```typescript
import { quickRun } from '@/lib/newman';

// Simple collection run
const result = await quickRun('./collections/my-collection.json');

if (result.success) {
  console.log('Collection run completed successfully!');
} else {
  console.error('Collection run failed:', result.error?.message);
}
```

### With Environment

```typescript
import { quickRun } from '@/lib/newman';

const result = await quickRun(
  './collections/api-tests.json',
  './environments/dev.json'
);
```

### Advanced Usage

```typescript
import { <PERSON><PERSON><PERSON><PERSON>, NewmanRunOptions } from '@/lib/newman';

const options: NewmanRunOptions = {
  collection: './collections/comprehensive-tests.json',
  environment: './environments/production.json',
  iterationCount: 3,
  delayRequest: 1000,
  reporters: ['cli', 'json'],
  reporter: {
    json: {
      export: './reports/test-results.json'
    }
  },
  globalVar: [
    { key: 'baseUrl', value: 'https://api.example.com' },
    { key: 'apiKey', value: process.env.API_KEY || '' }
  ]
};

const result = await NewmanHandler.runCollection(options);
```

## API Reference

### NewmanRunOptions Interface

```typescript
interface NewmanRunOptions {
  // Required
  collection: string | object;
  
  // Environment & Variables
  environment?: string | object;
  envVar?: Array<{ key: string; value: string }>;
  globals?: string | object;
  globalVar?: Array<{ key: string; value: string }>;
  
  // Execution Options
  iterationCount?: number;
  iterationData?: string;
  folder?: string | string[];
  
  // Timeout Options (milliseconds)
  timeout?: number;
  timeoutRequest?: number;
  timeoutScript?: number;
  delayRequest?: number;
  
  // Control Options
  bail?: boolean | string[];
  ignoreRedirects?: boolean;
  insecure?: boolean;
  
  // Reporting
  reporters?: string | string[];
  reporter?: { [key: string]: any };
  
  // SSL Options
  sslClientCert?: string;
  sslClientKey?: string;
  sslClientPassphrase?: string;
  
  // Advanced
  requestAgents?: { http?: any; https?: any };
  cookieJar?: string | object;
}
```

### Event Handlers

```typescript
import { NewmanEventHandlers } from '@/lib/newman';

const eventHandlers: NewmanEventHandlers = {
  start: (err, args) => console.log('Collection started'),
  beforeRequest: (err, args) => console.log(`Making request: ${args.request.url}`),
  request: (err, args) => console.log(`Response: ${args.response.code}`),
  assertion: (err, args) => {
    if (err) console.error(`Assertion failed: ${args.assertion}`);
  },
  done: (err, summary) => console.log('Collection completed')
};

const result = await NewmanHandler.runCollection(options, eventHandlers);
```

## Utility Functions

### Quick Functions

```typescript
import { quickRun, runWithReports, runLoadTest } from '@/lib/newman';

// Simple run
await quickRun('./collection.json');

// Run with comprehensive reporting
await runWithReports('./collection.json', './environment.json', './reports');

// Load testing
await runLoadTest('./collection.json', 50, 200); // 50 iterations, 200ms delay
```

### Validation

```typescript
import { NewmanHandler } from '@/lib/newman';

const validation = NewmanHandler.validateOptions(options);
if (!validation.valid) {
  console.error('Validation errors:', validation.errors);
}
```

### Retry Logic

```typescript
import { runWithRetry } from '@/lib/newman';

const result = await runWithRetry(options, 3, 2000); // 3 retries, 2s delay
```

## Examples

### CI/CD Integration

```typescript
import { NewmanUtils, NewmanHandler } from '@/lib/newman';

// Create CI-friendly configuration
const config = NewmanUtils.createCIConfig(
  './collections/api-tests.json',
  './environments/staging.json',
  './reports'
);

const result = await NewmanHandler.runCollection(config);

// Generate summary report
if (result.summary) {
  await NewmanUtils.saveSummaryReport(result.summary, './reports/summary.txt');
  
  const metrics = NewmanUtils.extractMetrics(result.summary);
  console.log(`Success Rate: ${metrics.requestSuccessRate}%`);
}
```

### Data-Driven Testing

```typescript
const options: NewmanRunOptions = {
  collection: './collections/user-tests.json',
  environment: './environments/test.json',
  iterationData: './data/users.csv', // CSV with test data
  iterationCount: 10,
  reporters: ['cli', 'json']
};

const result = await NewmanHandler.runCollection(options);
```

### SSL Client Certificates

```typescript
const options: NewmanRunOptions = {
  collection: './collections/secure-api.json',
  sslClientCert: './certs/client.crt',
  sslClientKey: './certs/client.key',
  sslClientPassphrase: process.env.SSL_PASSPHRASE,
  sslExtraCaCerts: './certs/ca-bundle.pem'
};
```

## File Structure

```
src/lib/newman/
├── index.ts              # Main exports and quick functions
├── newman-handler.ts     # Core Newman wrapper with types
├── newman-examples.ts    # Usage examples
├── newman-utils.ts       # Utility functions
└── README.md            # This documentation
```

## Error Handling

The Newman handler provides comprehensive error handling:

```typescript
try {
  const result = await NewmanHandler.runCollection(options);
  
  if (!result.success) {
    console.error('Collection failed:', result.error?.message);
    
    if (result.summary?.run.failures) {
      result.summary.run.failures.forEach(failure => {
        console.error(`- ${failure.error.message}`);
      });
    }
  }
} catch (error) {
  console.error('Unexpected error:', error);
}
```

## Environment Variables

You can use environment variables in your Newman configurations:

```typescript
const options: NewmanRunOptions = {
  collection: './collection.json',
  globalVar: [
    { key: 'apiKey', value: process.env.API_KEY || '' },
    { key: 'baseUrl', value: process.env.BASE_URL || 'http://localhost:3000' }
  ],
  envVar: [
    { key: 'environment', value: process.env.NODE_ENV || 'development' }
  ]
};
```

## Best Practices

1. **Always validate options** before running collections
2. **Use appropriate reporters** for your environment (CLI for development, JSON/JUnit for CI)
3. **Set reasonable timeouts** to prevent hanging tests
4. **Use environment variables** for sensitive data
5. **Implement retry logic** for flaky tests
6. **Generate comprehensive reports** for analysis
7. **Use event handlers** for monitoring and debugging

## Contributing

When adding new features to the Newman handler:

1. Update the TypeScript interfaces
2. Add comprehensive examples
3. Include proper error handling
4. Update this documentation
5. Add validation where appropriate

## License

This Newman handler follows the same license as the main project.
