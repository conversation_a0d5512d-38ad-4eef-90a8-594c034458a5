// Main Newman Handler exports
export {
  type <PERSON>RunOptions,
  type <PERSON>RunStats,
  type <PERSON>F<PERSON>ure,
  type <PERSON>Execution,
  type <PERSON>RunSummary,
  type <PERSON>EventType,
  type <PERSON>EventHandlers,
  type <PERSON>HandlerResult
} from '../newman-handler';

// Newman Examples exports
export {
  <PERSON>Examples,
  runNewmanCollection,
  runNewmanSimple,
  validateNewmanOptions
} from '../newman-examples';

// Re-export default handler
export { default as <PERSON>H<PERSON>ler } from '../newman-handler';
export { default as NewmanUtils } from '../newman-utils';

/**
 * Quick start functions for common use cases
 */

import { <PERSON>Hand<PERSON>, NewmanRunOptions } from '../newman-handler';
import { NewmanUtils } from '../newman-utils';

/**
 * Quick run function for simple collection execution
 * @param collectionPath Path to collection file or URL
 * @param environmentPath Optional path to environment file
 * @param options Additional options
 */
export async function quickRun(
  collectionPath: string,
  environmentPath?: string,
  options: Partial<NewmanRunOptions> = {}
) {
  return await NewmanHandler.runSimple(collectionPath, environmentPath, options);
}

/**
 * Run collection with comprehensive reporting
 * @param collectionPath Path to collection file or URL
 * @param environmentPath Optional path to environment file
 * @param reportDir Directory to save reports
 */
export async function runWithReports(
  collectionPath: string,
  environmentPath?: string,
  reportDir: string = './reports'
) {
  const config = NewmanUtils.createCIConfig(collectionPath, environmentPath, reportDir);
  const result = await NewmanHandler.runCollection(config);
  
  if (result.summary) {
    await NewmanUtils.saveSummaryReport(result.summary, `${reportDir}/summary.txt`);
  }
  
  return result;
}

/**
 * Run load test with specified iterations and delay
 * @param collectionPath Path to collection file or URL
 * @param iterations Number of iterations to run
 * @param delayMs Delay between requests in milliseconds
 */
export async function runLoadTest(
  collectionPath: string,
  iterations: number = 10,
  delayMs: number = 100
) {
  const config = NewmanUtils.createLoadTestConfig(collectionPath, iterations, delayMs);
  return await NewmanHandler.runCollection(config);
}

/**
 * Validate and run collection with error handling
 * @param options Newman run options
 */
export async function validateAndRun(options: NewmanRunOptions) {
  const validation = NewmanHandler.validateOptions(options);
  
  if (!validation.valid) {
    throw new Error(`Invalid options: ${validation.errors.join(', ')}`);
  }
  
  return await NewmanHandler.runCollection(options);
}

/**
 * Run collection with automatic retry on failure
 * @param options Newman run options
 * @param maxRetries Maximum number of retry attempts
 * @param retryDelay Delay between retries in milliseconds
 */
export async function runWithRetry(
  options: NewmanRunOptions,
  maxRetries: number = 3,
  retryDelay: number = 1000
) {
  return await NewmanUtils.runWithRetry(options, maxRetries, retryDelay);
}
