/**
 * Example test file demonstrating <PERSON> Handler usage
 * This file shows how to use the Newman Handler in various scenarios
 */

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>O<PERSON><PERSON>, NewmanUtils, quickRun } from './index';

/**
 * Example: Basic collection run
 */
export async function testBasicRun() {
  console.log('=== Testing Basic Collection Run ===');
  
  try {
    // Using the quick run function
    const result = await quickRun(
      'https://www.getpostman.com/collections/631643-f695cab7-6878-eb55-7943-ad88e1ccfd65-JsLv'
    );

    if (result.success && result.summary) {
      console.log('✅ Collection run successful');
      console.log(`Total requests: ${result.summary.run.stats.requests.total}`);
      console.log(`Failed requests: ${result.summary.run.stats.requests.failed}`);
      
      const metrics = NewmanUtils.extractMetrics(result.summary);
      console.log(`Success rate: ${metrics.requestSuccessRate}%`);
    } else {
      console.log('❌ Collection run failed:', result.error?.message);
    }
  } catch (error) {
    console.error('❌ Error running collection:', error);
  }
}

/**
 * Example: Advanced collection run with custom options
 */
export async function testAdvancedRun() {
  console.log('\n=== Testing Advanced Collection Run ===');
  
  const options: NewmanRunOptions = {
    collection: 'https://www.getpostman.com/collections/631643-f695cab7-6878-eb55-7943-ad88e1ccfd65-JsLv',
    iterationCount: 2,
    delayRequest: 500,
    reporters: ['cli', 'json'],
    reporter: {
      json: {
        export: './reports/advanced-test.json'
      }
    },
    globalVar: [
      { key: 'testMode', value: 'true' },
      { key: 'timestamp', value: new Date().toISOString() }
    ],
    timeout: 60000, // 1 minute timeout
    color: 'on'
  };

  try {
    // Validate options first
    const validation = NewmanHandler.validateOptions(options);
    if (!validation.valid) {
      console.log('❌ Invalid options:', validation.errors);
      return;
    }

    console.log('✅ Options validated successfully');

    const result = await NewmanHandler.runCollection(options, {
      start: () => console.log('🚀 Collection started'),
      beforeRequest: (err, args) => console.log(`📤 Request: ${args.request.method} ${args.request.url}`),
      request: (err, args) => {
        if (err) {
          console.log(`❌ Request failed: ${err.message}`);
        } else {
          console.log(`✅ Response: ${args.response.code} (${args.response.responseTime}ms)`);
        }
      },
      done: (err, summary) => {
        if (err) {
          console.log('❌ Collection completed with errors');
        } else {
          console.log('✅ Collection completed successfully');
        }
      }
    });

    if (result.success && result.summary) {
      console.log('\n📊 Final Results:');
      const report = NewmanUtils.generateSummaryReport(result.summary);
      console.log(report);
    }
  } catch (error) {
    console.error('❌ Error in advanced run:', error);
  }
}

/**
 * Example: Testing with mock data
 */
export async function testWithMockData() {
  console.log('\n=== Testing with Mock Collection ===');
  
  // Create a simple mock collection for testing
  const mockCollection = {
    info: {
      name: 'Mock Test Collection',
      description: 'A simple collection for testing Newman Handler'
    },
    item: [
      {
        name: 'Get Request Test',
        request: {
          method: 'GET',
          header: [],
          url: {
            raw: 'https://httpbin.org/get',
            host: ['httpbin', 'org'],
            path: ['get']
          }
        },
        event: [
          {
            listen: 'test',
            script: {
              exec: [
                'pm.test("Status code is 200", function () {',
                '    pm.response.to.have.status(200);',
                '});',
                '',
                'pm.test("Response has origin", function () {',
                '    pm.expect(pm.response.json()).to.have.property("origin");',
                '});'
              ]
            }
          }
        ]
      },
      {
        name: 'Post Request Test',
        request: {
          method: 'POST',
          header: [
            {
              key: 'Content-Type',
              value: 'application/json'
            }
          ],
          body: {
            mode: 'raw',
            raw: JSON.stringify({
              test: 'data',
              timestamp: '{{$timestamp}}'
            })
          },
          url: {
            raw: 'https://httpbin.org/post',
            host: ['httpbin', 'org'],
            path: ['post']
          }
        },
        event: [
          {
            listen: 'test',
            script: {
              exec: [
                'pm.test("Status code is 200", function () {',
                '    pm.response.to.have.status(200);',
                '});',
                '',
                'pm.test("Response contains posted data", function () {',
                '    const responseJson = pm.response.json();',
                '    pm.expect(responseJson.json).to.have.property("test", "data");',
                '});'
              ]
            }
          }
        ]
      }
    ]
  };

  const options: NewmanRunOptions = {
    collection: mockCollection,
    iterationCount: 1,
    reporters: ['cli'],
    delayRequest: 100,
    globalVar: [
      { key: 'testEnvironment', value: 'mock' }
    ]
  };

  try {
    const result = await NewmanHandler.runCollection(options);
    
    if (result.success && result.summary) {
      console.log('✅ Mock collection test successful');
      const metrics = NewmanUtils.extractMetrics(result.summary);
      console.log(`Requests: ${metrics.totalRequests}, Success Rate: ${metrics.requestSuccessRate}%`);
      console.log(`Tests: ${metrics.totalTests}, Success Rate: ${metrics.testSuccessRate}%`);
      console.log(`Average Response Time: ${metrics.averageResponseTime}ms`);
    } else {
      console.log('❌ Mock collection test failed:', result.error?.message);
    }
  } catch (error) {
    console.error('❌ Error in mock test:', error);
  }
}

/**
 * Example: Testing validation
 */
export async function testValidation() {
  console.log('\n=== Testing Validation ===');
  
  // Test with invalid options
  const invalidOptions: NewmanRunOptions = {
    collection: '', // Invalid: empty collection
    iterationCount: -1, // Invalid: negative iteration count
    timeout: -5000 // Invalid: negative timeout
  };

  const validation = NewmanHandler.validateOptions(invalidOptions);
  
  if (!validation.valid) {
    console.log('✅ Validation correctly caught errors:');
    validation.errors.forEach(error => console.log(`  - ${error}`));
  } else {
    console.log('❌ Validation should have failed but didn\'t');
  }

  // Test with valid options
  const validOptions: NewmanRunOptions = {
    collection: 'https://www.getpostman.com/collections/631643-f695cab7-6878-eb55-7943-ad88e1ccfd65-JsLv',
    iterationCount: 1,
    timeout: 30000
  };

  const validValidation = NewmanHandler.validateOptions(validOptions);
  
  if (validValidation.valid) {
    console.log('✅ Valid options passed validation');
  } else {
    console.log('❌ Valid options failed validation:', validValidation.errors);
  }
}

/**
 * Run all tests
 */
export async function runAllTests() {
  console.log('🧪 Starting Newman Handler Tests\n');
  
  await testValidation();
  await testBasicRun();
  await testAdvancedRun();
  await testWithMockData();
  
  console.log('\n✅ All tests completed!');
}

// Export individual test functions for selective testing
// If running this file directly (for testing purposes)
if (require.main === module) {
  runAllTests().catch(console.error);
}
