import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Exclude <PERSON> and related packages from client-side bundling
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // These packages should only run on the server-side
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      };

      // Exclude <PERSON> and its dependencies from client bundle
      config.externals = config.externals || [];
      config.externals.push({
        newman: 'newman',
        'postman-sandbox': 'postman-sandbox',
        'postman-runtime': 'postman-runtime',
        'postman-collection': 'postman-collection',
        uvm: 'uvm',
        'teleport-javascript': 'teleport-javascript',
        terser: 'terser',
      });
    }

    return config;
  },

  // Ensure server-side only packages are not included in client bundle
  experimental: {
    serverComponentsExternalPackages: [
      'newman',
      'postman-sandbox',
      'postman-runtime',
      'postman-collection',
      'uvm',
      'teleport-javascript'
    ],
  },
};

export default nextConfig;
